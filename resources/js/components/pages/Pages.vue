<template>
    <span v-if="components.length" class="com-background">
        <component
            v-for="(comp, index) in components"
            :key="index"
            :is="comp.layout"
            :attributes="comp"
        >
        </component>
    </span>
</template>

<script>
import HeroImageSlider from '../components/HeroImageSlider';
import TrackComponent from '../components/TrackComponent';
import TrackSideComponent from '../components/TrackSideComponent';
import Divider from '../components/Divider';
import MenuTrack from '../components/MenuTrack';
import DepartmentTrack from '../components/DepartmentTrack';
import DepartmentHeader from '../components/DepartmentHeader';
import Brands from '../components/Brands';
import Wysiwyg from '../components/Wysiwyg';
import TextImage from '../components/TextImage';
import ImageGrid from '../components/ImageGrid';
import BackgroundImageWysiwyg from '../components/BackgroundImageWysiwyg';
export default {
    components: {
        HeroImageSlider,
        TrackComponent,
        TrackSideComponent,
        Divider,
        MenuTrack,
        DepartmentTrack,
        DepartmentHeader,
        Brands,
        Wysiwyg,
        TextImage,
        ImageGrid,
        BackgroundImageWysiwyg,
    },
    data() {
        return {
            layouts: {},
            components: [],
            conversionsByCount: {
                1: 1430,
                2: 780,
                other: 470,
            },
        };
    },
    methods: {
        getMedia(
            key,
            component,
            mobile = false,
            imagesCount = null,
            tablet = false
        ) {
            let mediaUrl = '';
            let media = '';
            if (mobile) {
                media = this.layouts.media.find(
                    (m) =>
                        m.name.slice(0, 16) === key &&
                        (m.collection_name == 'mobile' ||
                            component === 'department-header')
                ); //|| media
                if (media) {
                    let imageConversion = '';
                    if (tablet) {
                        imageConversion = '780';
                    } else {
                        imageConversion = '470';
                    }
                    mediaUrl = `${import.meta.env.VITE_AWS_URL}/media/${media.id}/conversions/${this.getFileName(media.file_name)}-${imageConversion}.${this.getFileExtension()}`;
                }
            } else {
                media = this.layouts.media.find(
                    (m) =>
                        m.name.slice(0, 16) === key &&
                        m.collection_name != 'mobile'
                );
                if (media) {
                    let imageConversion = '';
                    if (imagesCount) {
                        imageConversion = this.conversionsByCount[imagesCount];
                    } else if (component === 'track-side-component') {
                        imageConversion = '470';
                    } else {
                        imageConversion = '1800';
                    }
                    mediaUrl = `${import.meta.env.VITE_AWS_URL}/media/${media.id}/conversions/${this.getFileName(media.file_name)}-${imageConversion}.${this.getFileExtension()}`;
                }
            }
            return mediaUrl;
        },

        getComponents(urlPath) {
            axios('/api/pages' + urlPath)
                .then((resp) => {
                    if (resp.data.components.length > 0) {
                        this.components = [];
                        this.layouts = resp.data;

                        let title = this.layouts.title || this.layouts.name;
                        title += ' | Eichlers';
                        document.title = title;
                        this.setTags();
                        this.layouts.components.forEach((c) => {
                            if (c.layout === 'hero-image-slider') {
                                c.attributes.banners.forEach((b) => {
                                    b.attributes['media'] = this.getMedia(
                                        b.key,
                                        c.layout
                                    );
                                    b.attributes['mobileMedia'] = this.getMedia(
                                        b.key,
                                        c.layout,
                                        true
                                    );
                                });
                                this.components.push({
                                    ...c.attributes,
                                    ...{ layout: c.layout },
                                });
                            } else if (c.layout === 'track-side-component') {
                                this.components.push({
                                    ...c.attributes,
                                    ...{
                                        media: this.getMedia(c.key, c.layout),
                                    },
                                    ...{
                                        mobileMedia: this.getMedia(
                                            c.key,
                                            c.layout,
                                            true
                                        ),
                                    },
                                    ...{
                                        tabletMedia: this.getMedia(
                                            c.key,
                                            c.layout,
                                            true,
                                            null,
                                            true
                                        ),
                                    },
                                    ...{ layout: c.layout },
                                });
                            } else if (c.layout === 'image-grid') {
                                let imagesCount =
                                    c.attributes.images.length > 2
                                        ? 'other'
                                        : c.attributes.images.length;

                                c.attributes.images.forEach((i) => {
                                    i.attributes['media'] = this.getMedia(
                                        i.key,
                                        c.layout,
                                        false,
                                        imagesCount
                                    );
                                    i.attributes['mobileMedia'] = this.getMedia(
                                        i.key,
                                        c.layout,
                                        true,
                                        imagesCount
                                    );
                                });
                                this.components.push({
                                    ...c.attributes,
                                    ...{ layout: c.layout },
                                });
                            } else {
                                this.components.push({
                                    ...c.attributes,
                                    ...{
                                        media: this.getMedia(c.key, c.layout),
                                    },
                                    ...{
                                        mobileMedia: this.getMedia(
                                            c.key,
                                            c.layout,
                                            true
                                        ),
                                    },
                                    ...{ layout: c.layout },
                                });
                            }
                        });
                    } else {
                        this.layouts = {};
                        this.components = [];
                    }
                    // document.title = this.layouts.title ? this.layouts.title : "Eichlers";
                })
                .catch((err) => {
                    handleError(err, this.$router);
                });
        },

        getOgImageUrl() {
            // Get the first media item from the media collection
            const firstMedia = _.get(this.layouts, 'media[0]');
            if (firstMedia) {
                // Generate 470px conversion URL using the same pattern as getMedia method
                const fileName = this.getFileName(firstMedia.file_name);
                const fileExtension = this.getFileExtension();
                return `${import.meta.env.VITE_AWS_URL}/media/${firstMedia.id}/conversions/${fileName}-470.${fileExtension}`;
            }
            // Fallback to original media_urls[0] if no media found
            return _.get(this.layouts, 'media_urls[0]');
        },

        setTags() {
            this.createTag(
                'property',
                'og:title',
                _.get(this.layouts, 'title')
            );
            this.createTag('property', 'og:site_name', '1800eichlers.com');
            this.createTag('property', 'og:type', 'website');
            this.createTag(
                'property',
                'og:url',
                window.location.origin + _.get(this.layouts, 'path')
            );
            this.createTag(
                'property',
                'og:image',
                this.getOgImageUrl()
            );
            this.createTag(
                'property',
                'og:description',
                _.get(this.layouts, 'description') || ' '
            );
            this.createTag('name', 'twitter:card', 'summary');
            this.createTag(
                'name',
                'twitter:site',
                window.location.origin + _.get(this.layouts, 'path')
            );
            this.createTag(
                'name',
                'twitter:title',
                _.get(this.layouts, 'title')
            );
            this.createTag(
                'name',
                'twitter:description',
                _.get(this.layouts, 'description') || ' '
            );
            this.createTag(
                'name',
                'twitter:image:src',
                this.getOgImageUrl()
            );
            this.createTag('itemprop', 'name', _.get(this.layouts, 'title'));
            this.createTag(
                'itemprop',
                'description',
                _.get(this.layouts, 'description') || ' '
            );
            this.createTag(
                'name',
                'description',
                _.get(this.layouts, 'description') || ' '
            );
            this.createTag(
                'itemprop',
                'image',
                this.getOgImageUrl()
            );
        },
        createTag(property, value, content) {
            const meta = document.querySelector(
                `[data-meta-id="${property}-${value}"]`
            );
            if (meta) {
                meta.content = content;
            } else {
                var link = document.createElement('meta');
                link.setAttribute(property, value);
                link.setAttribute(`data-meta-id`, `${property}-${value}`);
                link.content = content;
                document.head.insertBefore(
                    link,
                    document.head.firstElementChild
                );
            }
        },
        getFileName(filename) {
            const lastDot = filename.lastIndexOf('.');
            return lastDot === -1 ? filename : filename.slice(0, lastDot);
        },
        getFileExtension() {
            return 'jpg';
        }
    },
    mounted() {
        this.getComponents(this.$route.path);
    },

    metaInfo() {
        // return{
        //     meta: [
        //     {property: 'og:title', content: _.get(this.layouts,'title')},
        //     {property: 'og:site_name', content: '1800eichlers.com'},
        //     {property: 'og:type', content: 'website'},
        //     {property: 'og:url', content: window.location.origin + _.get(this.layouts,'path')},
        //     {property: 'og:image', content: window.location.origin + _.get(this.layouts,'media_urls[0]')},
        //     {property: 'og:description', content: _.get(this.layouts,'description') || ' '},
        //     {name: 'twitter:card', content: 'summary'},
        //     {name: 'twitter:site', content: window.location.origin + _.get(this.layouts,'path')},
        //     {name: 'twitter:title', content: _.get(this.layouts,'title')},
        //     {name: 'twitter:description', content: _.get(this.layouts,'description') || ' '},
        //     {name: 'twitter:image:src', content: window.location.origin + _.get(this.layouts,'media_urls[0]')},
        //     {itemprop: 'name', content: _.get(this.layouts,'title')},
        //     {itemprop: 'description', content: _.get(this.layouts,'description') || ' '},
        //     {itemprop: 'image', content: window.location.origin + _.get(this.layouts,'media_urls[0]')},
        //     {name: 'description', content: _.get(this.layouts,'description') || ' '},
        //   ]
        // }
    },

    watch: {
        $route: {
            handler() {
                this.getComponents(this.$route.path);
            },
            deep: true,
        },
    },
};
</script>

<style></style>