<template>
    <span v-if="components.length" class="com-background">
        <component
            v-for="(comp, index) in components"
            :key="index"
            :is="comp.layout"
            :attributes="comp"
        >
        </component>
    </span>
</template>

<script>
import HeroImageSlider from '../components/HeroImageSlider';
import TrackComponent from '../components/TrackComponent';
import TrackSideComponent from '../components/TrackSideComponent';
import Divider from '../components/Divider';
import MenuTrack from '../components/MenuTrack';
import DepartmentTrack from '../components/DepartmentTrack';
import DepartmentHeader from '../components/DepartmentHeader';
import Brands from '../components/Brands';
import Wysiwyg from '../components/Wysiwyg';
import TextImage from '../components/TextImage';
import ImageGrid from '../components/ImageGrid';
import BackgroundImageWysiwyg from '../components/BackgroundImageWysiwyg';

export default {
    data() {
        return {
            layouts: {},
            components: [],
            conversionsByCount: {
                1: 1430,
                2: 780,
                other: 470,
            },
            initial: true,
        };
    },

    methods: {
        sleep(amount) {
            return new Promise((resolve) => {
                setTimeout(resolve, amount);
            });
        },

        getMedia(
            key,
            component,
            mobile = false,
            imagesCount = null,
            tablet = false
        ) {
            let mediaUrl = '';
            let media = '';
            if (mobile) {
                media = this.layouts.media.find(
                    (m) =>
                        m.name.slice(0, 16) === key &&
                        (m.collection_name == 'mobile' ||
                            component === 'department-header')
                ); //|| media
                if (media) {
                    let imageConversion = '';
                    if (tablet) {
                        imageConversion = '780';
                    } else {
                        imageConversion = '470';
                    }
                    mediaUrl = `${import.meta.env.VITE_AWS_URL}/media/${media.id}/conversions/${this.getFileName(media.file_name)}-${imageConversion}.${this.getFileExtension()}`;
                }
            } else {
                media = this.layouts.media.find(
                    (m) =>
                        m.name.slice(0, 16) === key &&
                        m.collection_name != 'mobile'
                );
                if (media) {
                    let imageConversion = '';
                    if (imagesCount) {
                        imageConversion = this.conversionsByCount[imagesCount];
                    } else if (component === 'track-side-component') {
                        imageConversion = '470';
                    } else {
                        imageConversion = '1800';
                    }
                    mediaUrl = `${import.meta.env.VITE_AWS_URL}/media/${media.id}/conversions/${this.getFileName(media.file_name)}-${imageConversion}.${this.getFileExtension()}`;
                }
            }
            return mediaUrl;
        },

        async init() {
            //for(const [i,c] of this.layouts.components.entries()){
            for (const c of this.layouts.components) {
                // if(i > 4){
                //   //await this.sleep(500)
                // }
                if (c.layout === 'hero-image-slider') {
                    c.attributes.banners.forEach((b) => {
                        b.attributes['media'] = this.getMedia(b.key, c.layout);
                        b.attributes['mobileMedia'] = this.getMedia(
                            b.key,
                            c.layout,
                            true
                        );
                    });
                    this.components.push({
                        ...c.attributes,
                        ...{layout: c.layout},
                    });
                } else if (c.layout === 'track-side-component') {
                    this.components.push({
                        ...c.attributes,
                        ...{media: this.getMedia(c.key, c.layout)},
                        ...{
                            mobileMedia: this.getMedia(c.key, c.layout, true),
                        },
                        ...{
                            tabletMedia: this.getMedia(
                                c.key,
                                c.layout,
                                true,
                                null,
                                true
                            ),
                        },
                        ...{layout: c.layout},
                    });
                } else if (c.layout === 'image-grid') {
                    let imagesCount =
                        c.attributes.images.length > 2
                            ? 'other'
                            : c.attributes.images.length;

                    c.attributes.images.forEach((i) => {
                        i.attributes['media'] = this.getMedia(
                            i.key,
                            c.layout,
                            false,
                            imagesCount
                        );
                        i.attributes['mobileMedia'] = this.getMedia(
                            i.key,
                            c.layout,
                            true,
                            imagesCount
                        );
                    });
                    this.components.push({
                        ...c.attributes,
                        ...{layout: c.layout},
                    });
                } else {
                    this.components.push({
                        ...c.attributes,
                        ...{media: this.getMedia(c.key, c.layout)},
                        ...{
                            mobileMedia: this.getMedia(c.key, c.layout, true),
                        },
                        ...{layout: c.layout},
                    });
                }
            }
        },

        getComponents() {
            axios('/api/pages')
                .then((resp) => {
                    this.layouts = resp.data;
                    this.init();
                    //this.layouts.components.splice(3,this.layouts.components.length)
                    let title = this.layouts.title || this.layouts.name;
                    title += ' | Eichlers';
                    document.title = title;
                    this.setTags();
                })
                .catch((error) => {
                    handleError(error, this.$router);
                });
        },

        getOgImageUrl() {
            // Get the first media item from the media collection
            const firstMedia = _.get(this.layouts, 'media[0]');
            if (firstMedia) {
                // Generate 470px conversion URL using the same pattern as getMedia method
                const fileName = this.getFileName(firstMedia.file_name);
                const fileExtension = this.getFileExtension();
                return `${import.meta.env.VITE_AWS_URL}/media/${firstMedia.id}/conversions/${fileName}-470.${fileExtension}`;
            }
            // Fallback to original media_urls[0] if no media found
            return _.get(this.layouts, 'media_urls[0]');
        },

        setTags() {
            this.createTag(
                'property',
                'og:title',
                _.get(this.layouts, 'title')
            );
            this.createTag('property', 'og:site_name', '1800Eichlers.com');
            this.createTag('property', 'og:type', 'website');
            this.createTag(
                'property',
                'og:url',
                window.location.origin + _.get(this.layouts, 'path')
            );
            this.createTag(
                'property',
                'og:image',
                this.getOgImageUrl()
            );
            this.createTag(
                'property',
                'og:description',
                _.get(this.layouts, 'description') || ' '
            );
            this.createTag('name', 'twitter:card', 'summary');
            this.createTag(
                'name',
                'twitter:site',
                window.location.origin + _.get(this.layouts, 'path')
            );
            this.createTag(
                'name',
                'twitter:title',
                _.get(this.layouts, 'title')
            );
            this.createTag(
                'name',
                'twitter:description',
                _.get(this.layouts, 'description') || ' '
            );
            this.createTag(
                'name',
                'twitter:image:src',
                this.getOgImageUrl()
            );
            this.createTag('itemprop', 'name', _.get(this.layouts, 'title'));
            this.createTag(
                'itemprop',
                'description',
                _.get(this.layouts, 'description') || ' '
            );
            this.createTag(
                'name',
                'description',
                _.get(this.layouts, 'description') || ' '
            );
            this.createTag(
                'itemprop',
                'image',
                this.getOgImageUrl()
            );
        },

        createTag(property, value, content) {
            const meta = document.querySelector(
                `[data-meta-id="${property}-${value}"]`
            );
            if (meta) {
                meta.content = content;
            } else {
                var link = document.createElement('meta');
                link.setAttribute(property, value);
                link.setAttribute(`data-meta-id`, `${property}-${value}`);
                link.content = content;
                document.head.insertBefore(
                    link,
                    document.head.firstElementChild
                );
            }
        },
        getFileName(filename) {
            const lastDot = filename.lastIndexOf('.');
            return lastDot === -1 ? filename : filename.slice(0, lastDot);
        },
        getFileExtension() {
            return 'jpg';
        }
    },

    mounted() {
        this.getComponents();
        if (this.$route.query.chat) {
            Beacon('open');
        }
    },

    components: {
        HeroImageSlider,
        TrackComponent,
        TrackSideComponent,
        Divider,
        MenuTrack,
        DepartmentTrack,
        DepartmentHeader,
        Brands,
        Wysiwyg,
        TextImage,
        ImageGrid,
        BackgroundImageWysiwyg,
    },
};
</script>

<style></style>