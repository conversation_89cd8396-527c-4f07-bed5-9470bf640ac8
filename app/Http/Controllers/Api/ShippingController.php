<?php

namespace App\Http\Controllers\Api;

use App\ClosedDay;
use App\Discount;
use App\FulfillmentSchematic;
use App\Http\Controllers\Controller;
use App\ShippingOption;
use App\ShippingOptionShippingZone;
use App\ShippingZone;
use App\ZipCode;
use DateInterval;
use DateTime;
use DateTimeZone;
use Exception;
use Illuminate\Http\Request;
use Throwable;

class ShippingController extends Controller
{
    public function index(Request $request)
    {
        $total = $request->total;
        $billable_weight = $request->billable_weight;

        $location = [
            'postal_code' => $request->zip_code,
            'country' => $request->country,
        ];
        $options = $this->getOptions($billable_weight, $total, null, $location, null, 0);

        return [
            'type' => 'national',
            'store_expires' => $this->refreshPickup(),
            'fulfillment_expires' => $this->refreshDelivery(),
            'pickup' => $options->where('delivery', false)->first(),
            'cheapest' => $options->where('delivery', true)->sortBy('price')->first(),
            'fastest' => $options->where('delivery', true)->sortBy('days')->first(),
            'over_price' => $options
                ->where('free_shipping_above', '!==', null)
                ->where('delivery', true)
                ->pluck('free_shipping_above')
                ->min(),
            'free_over' => $options
                ->where('free_shipping_above', '!==', null)
                ->where('delivery', true)
                ->sortBy(function ($item) {
                    return data_get($item, 'free_shipping_above') . '-' . data_get($item, 'days');
                })
                ->first(),
            'shipping_ids_not_excluded_from_free_shipping' => settings()->getValue(
                'exclude_from_free_shipping_does_not_apply_to'
            )
        ];
    }

    public function getOptions(
        $billable_weight = 0,
        $total = 0,
        $excluded_billable_weight,
        $location,
        $discount,
        $personalDuration
    ) {
        $zip = $location['postal_code'];

        try {
            $options = ZipCode::getCode($zip)->shippingZone->options()->active()->get();
        } catch (Throwable $e) {
            abort(404);
        }
        return $options->map(
            function ($option) use (
                $billable_weight,
                $total,
                $excluded_billable_weight,
                $location,
                $discount,
                $personalDuration
            ) {
                if (collect(
                    explode(',', settings()->getValue('exclude_from_free_shipping_does_not_apply_to'))
                )->contains($option->shipping_option_id)) {
                    $excluded_billable_weight = null;
                }

                $now = now();
                $fulfillment = $option->shippingOption->fulfillmentSchematic;
                $day = strtolower($now->format('l'));
                $time = ($now->format('H') + $now->format('i') / 60);
                $close = $day . '_close';
                $open = $day . '_open';
                $todays_ful = $fulfillment;
                if ($closed_day = ClosedDay::whereDate('date', $now)->first()) {
                    $open = 'open';
                    $close = 'close';
                    $todays_ful = $closed_day;
                    $todays_ful->cutoff = $fulfillment->cutoff;
                }
                if (
                    $time >= $todays_ful->$open
                    && $time < ($todays_ful->$close - $todays_ful->cutoff)
                    && ($time + $option->duration) < $todays_ful->$close
                ) {
                    if ($option->duration_is_hourly) {
                        $date = $now->addMinutes($option->duration * 60);
                    } else {
                        $date = $now->addDay($option->duration)->startOfDay();
                    }
                } elseif ($time < $todays_ful->$open) {
                    $date = $now
                        ->setHours($todays_ful->$open)
                        ->setMinutes(($todays_ful->$open - floor($todays_ful->$open)) * 60);
                    if ($option->duration_is_hourly) {
                        $date = $date->addMinutes($option->duration * 60);
                    } else {
                        $date = $date->addDay($option->duration)->startOfDay();
                    }
                } else {
                    $a = 1;
                    $now = now();
                    while ($a <= 10) {
                        $now = $now->addDay();
                        $day = strtolower($now->format('l')) . '_open';

                        if (
                            (ClosedDay::whereDate('date', $now)->first()
                                || !$fulfillment->$day)
                            // && collect(explode(',', $option->shippingOption->operational_days))->contains($now->dayOfWeek)
                        ) {
                            $a++;
                        } else {
                            break;
                        }
                    }
                    $date = now()
                        ->addDay($a)
                        ->setHours(0)
                        ->setMinute(0)
                        ->setSeconds(0);

                    if ($option->duration_is_hourly) {
                        $date = $date->setHour($fulfillment->$open)->addMinutes($option->duration * 60);
                    } else {
                        $date = $date->addDay($option->duration)->startOfDay();
                    }
                }

                if ($option->free_shipping_above && $option->free_shipping_above <= ($total - data_get(
                            $discount,
                            'savings'
                        ))) {
                    $billable_weight = $excluded_billable_weight;
                }
                if ($billable_weight === null) {
                    $price = 0;
                } else {
                    $price = $option->type == 'flat'
                        ? $option->base_rate + $option->price_per_pound
                        : $option->base_rate + ($option->priceByPound($billable_weight) ?? 0);
                }

                $delivery = $option->shippingOption->delivery;

                if ($discount && $excluded_billable_weight === null) {
                    $free = data_get($discount, 'shipping_options');

                    if ($free && collect($free)->pluck('id')->contains($option->shipping_option_id)) {
                        $price = 0;
                    }
                }

                $date = $option->duration_is_hourly ? $date->addMinutes($personalDuration * 24 * 60) : $date->addDays(
                    $personalDuration
                );

                $a = $a ?? 0;

                $days_diff = today()->addDay($a)->diffInDays($date);

                $add = 0;

                if ($days_diff >= 1) {
                    collect(range($a, ceil($days_diff)))->each(function ($d) use (&$add, $option, $fulfillment) {
                        $new_date = today()->addDay($d);
                        $day = strtolower($new_date->format('l')) . '_open';
                        if (!collect(explode(',', $option->shippingOption->operational_days))->contains(
                            $new_date->dayOfWeek
                        )) { //} && !ClosedDay::whereDate('date', $new_date)->first()) {
                            $add++;
                        }
                    });
                }
                $date = $date->addDays($add);

                $date = $this->generateOperationalDays($date, $option);

                return [
                    'price' => $price,
                    'delivery' => $delivery,
                    'estimated_arrival' => $date->toDateTimeString(),
                    'base_rate' => $option->base_rate,
                    'id' => $option->shipping_option_id,
                    'days' => today()->diffInDays($date),
                    'hours' => now()->diffInHours($date),
                    'price_per_pound' => $option->price_per_pound,
                    'name' => $option->shippingOption->internal_name,
                    'free_shipping_above' => $option->free_shipping_above,
                    'date' => $date->minute(ceil($date->format('i') / 15) * 15)->calendar(),
                ];
            }
        )->filter()->values();
    }

    public function generateOperationalDays($date, $option)
    {
        $a = 1;
        $date = $date;
        while ($a <= 10) {
            $day = strtolower($date->format('l')) . '_open';

            if (!collect(explode(',', $option->shippingOption->operational_days))->contains($date->dayOfWeek)) {
                $a++;
                $date = $date->addDay()->startOfDay();
            } else if (!!$option->operational_days && !collect(explode(',', $option->operational_days))->contains($date->dayOfWeek)) {
                $a++;
                $date = $date->addDay()->startOfDay();
            } else {
                break;
            }
        }
        return $date;
    }

    public function refreshPickup()
    {
        $now = new DateTime();
        $now->setTimezone(new DateTimeZone('America/New_York'));
        $day = $now->format('l');
        $time = doubleval($now->format('H')) + (doubleval($now->format('i')) / 60);
        $delivery = FulfillmentSchematic::where('delivery', false)->first();

        if (
            !$this->isOffHours([['fulfillment_schematic' => $delivery]], $day, $time)
            && !$this->isClosedDay($now, 'delivery')
        ) {
            $diff = $delivery->{strtolower($day) . '_close'}
                - ($delivery->cutoff_is_hourly ? $delivery->cutoff : $delivery->cutoff * 24);

            $date = (new DateTime)->setTime((int)$diff, ($diff - (int)$diff) * 100);
            return $date->format('Y-m-d') . 'T' . $date->format('G:i');
        } else {
            $day = $this->findNextAvailableDay([['fulfillment_schematic' => $delivery]], $now);

            return $day->format('Y-m-d') . 'T' . $day->format('G:i');
        }
    }

    /**
     * Determines whether all shipping options in an array are invalid because they are during off hours for a specific day and time
     * @param array $shippingOptions - The array of shipping options to be processed
     * @param string $day - The day of the week of the order
     * @param double $time - The time of the order
     * @return bool
     */
    public function isOffHours($shippingOptions, $day, $time)
    {
        foreach ($shippingOptions as $shippingOption) {
            $fulfillmentSchematic = $shippingOption['fulfillment_schematic'];
            switch ($day) {
                case 'Sunday':
                    $bothNull = is_null($fulfillmentSchematic->sunday_open) && is_null(
                            $fulfillmentSchematic->sunday_close
                        );
                    $beforeOpen = !is_null(
                            $fulfillmentSchematic->sunday_open
                        ) && $time < $fulfillmentSchematic->sunday_open;
                    $afterClose = !is_null(
                            $fulfillmentSchematic->sunday_close
                        ) && $time > $fulfillmentSchematic->sunday_close;
                    if (!$bothNull && !$beforeOpen && !$afterClose) {
                        return false;
                    }
                    break;
                case 'Monday':
                    $bothNull = is_null($fulfillmentSchematic->monday_open) && is_null(
                            $fulfillmentSchematic->monday_close
                        );
                    $beforeOpen = !is_null(
                            $fulfillmentSchematic->monday_open
                        ) && $time < $fulfillmentSchematic->monday_open;
                    $afterClose = !is_null(
                            $fulfillmentSchematic->monday_close
                        ) && $time > $fulfillmentSchematic->monday_close;
                    if (!$bothNull && !$beforeOpen && !$afterClose) {
                        return false;
                    }
                    break;
                case 'Tuesday':
                    $bothNull = is_null($fulfillmentSchematic->tuesday_open) && is_null(
                            $fulfillmentSchematic->tuesday_close
                        );
                    $beforeOpen = !is_null(
                            $fulfillmentSchematic->tuesday_open
                        ) && $time < $fulfillmentSchematic->tuesday_open;
                    $afterClose = !is_null(
                            $fulfillmentSchematic->tuesday_close
                        ) && $time > $fulfillmentSchematic->tuesday_close;
                    if (!$bothNull && !$beforeOpen && !$afterClose) {
                        return false;
                    }
                    break;
                case 'Wednesday':
                    $bothNull = is_null($fulfillmentSchematic->wednesday_open) && is_null(
                            $fulfillmentSchematic->wednesday_close
                        );
                    $beforeOpen = !is_null(
                            $fulfillmentSchematic->wednesday_open
                        ) && $time < $fulfillmentSchematic->wednesday_open;
                    $afterClose = !is_null(
                            $fulfillmentSchematic->wednesday_close
                        ) && $time > $fulfillmentSchematic->wednesday_close;
                    if (!$bothNull && !$beforeOpen && !$afterClose) {
                        return false;
                    }
                    break;
                case 'Thursday':
                    $bothNull = is_null($fulfillmentSchematic->thursday_open) && is_null(
                            $fulfillmentSchematic->thursday_close
                        );
                    $beforeOpen = !is_null(
                            $fulfillmentSchematic->thursday_open
                        ) && $time < $fulfillmentSchematic->thursday_open;
                    $afterClose = !is_null(
                            $fulfillmentSchematic->thursday_close
                        ) && $time > $fulfillmentSchematic->thursday_close;
                    if (!$bothNull && !$beforeOpen && !$afterClose) {
                        return false;
                    }
                    break;
                case 'Friday':
                    $bothNull = is_null($fulfillmentSchematic->friday_open) && is_null(
                            $fulfillmentSchematic->friday_close
                        );
                    $beforeOpen = !is_null(
                            $fulfillmentSchematic->friday_open
                        ) && $time < $fulfillmentSchematic->friday_open;
                    $afterClose = !is_null(
                            $fulfillmentSchematic->friday_close
                        ) && $time > $fulfillmentSchematic->friday_close;
                    if (!$bothNull && !$beforeOpen && !$afterClose) {
                        return false;
                    }
                    break;
                case 'Saturday':
                    $bothNull = is_null($fulfillmentSchematic->saturday_open) && is_null(
                            $fulfillmentSchematic->saturday_close
                        );
                    $beforeOpen = !is_null(
                            $fulfillmentSchematic->saturday_open
                        ) && $time < $fulfillmentSchematic->saturday_open;
                    $afterClose = !is_null(
                            $fulfillmentSchematic->saturday_close
                        ) && $time > $fulfillmentSchematic->saturday_close;
                    if (!$bothNull && !$beforeOpen && !$afterClose) {
                        return false;
                    }
                    break;
                default:
                    return true;
            }
        }
        return true;
    }

    /**
     * Determines whether the provided date is a closed day
     * @param DateTime $now
     * @return bool
     */
    public function isClosedDay($now, $center = 'delivery')
    {
        if ($day = ClosedDay::where(['date' => $now->format('Y-m-d')])->first()) {
            if ($day->{$center} == false) {
                return false;
            }

            $time = doubleval($now->format('H')) + (doubleval($now->format('i')) / 60);
            if ($time < $day->open || $time > $day->close) {
                return true;
            }
        }
        return false;
    }

    /**
     * Attempts to find the next available calendar day and earliest start time
     * for which the store is not closed and there are start times available (default start time: 8 AM).
     * Returns null if 30 days are checked and no options are found.
     * @param array $shippingOptions - The shipping options to factor into processing
     * @param DateTime $now - The reference date from which to start checking for another available date
     * @return DateTime|null
     * @throws Exception
     */
    public function findNextAvailableDay($shippingOptions, $now)
    {
        $startTime = null;
        $currDay = $now;
        $nextDay = null;
        $i = 0;
        while (is_null($startTime) && $i < 30) {
            $i++;
            $nextDay = $currDay->add(new DateInterval('P1D'));
            if (!$this->isClosedDay($nextDay) && $this->hasStartTimes($shippingOptions, $nextDay->format('l'))) {
                $startTime = $this->getEarliestStartTime($shippingOptions, $nextDay->format('l'));
                $hours = floor($startTime);
                $nextDay->setTime($hours, floor($startTime - $hours));
            }
        }
        return $nextDay;
    }

    /**
     * Determines whether any shipping option provided has a start time on the given day
     * @param array $shippingOptions - The shipping options to process
     * @param string $day - The day of the week
     * @return bool
     */
    public function hasStartTimes($shippingOptions, $day)
    {
        foreach ($shippingOptions as $shippingOption) {
            $fulfillmentSchematic = $shippingOption['fulfillment_schematic'];
            switch ($day) {
                case 'Sunday':
                    if (!(is_null($fulfillmentSchematic->sunday_open) && is_null(
                            $fulfillmentSchematic->sunday_close
                        ))) {
                        return true;
                    }
                    break;
                case 'Monday':
                    if (!(is_null($fulfillmentSchematic->monday_open) && is_null(
                            $fulfillmentSchematic->monday_close
                        ))) {
                        return true;
                    }
                    break;
                case 'Tuesday':
                    if (!(is_null($fulfillmentSchematic->tuesday_open) && is_null(
                            $fulfillmentSchematic->tuesday_close
                        ))) {
                        return true;
                    }
                    break;
                case 'Wednesday':
                    if (!(is_null($fulfillmentSchematic->wednesday_open) && is_null(
                            $fulfillmentSchematic->wednesday_close
                        ))) {
                        return true;
                    }
                    break;
                case 'Thursday':
                    if (!(is_null($fulfillmentSchematic->thursday_open) && is_null(
                            $fulfillmentSchematic->thursday_close
                        ))) {
                        return true;
                    }
                    break;
                case 'Friday':
                    if (!(is_null($fulfillmentSchematic->friday_open) && is_null(
                            $fulfillmentSchematic->friday_close
                        ))) {
                        return true;
                    }
                    break;
                case 'Saturday':
                    if (!(is_null($fulfillmentSchematic->saturday_open) && is_null(
                            $fulfillmentSchematic->saturday_close
                        ))) {
                        return true;
                    }
                    break;
                default:
                    return false;
            }
        }
        return false;
    }

    /**
     * Determines the earliest start time among all shipping options provided
     * @param array $shippingOptions - The shipping options to process
     * @param string $day - The day of the week
     * @return float|string
     */
    public function getEarliestStartTime($shippingOptions, $day)
    {
        $earliestStartTime = 23.99;
        foreach ($shippingOptions as $shippingOption) {
            $fulfillmentSchematic = $shippingOption['fulfillment_schematic'];
            switch ($day) {
                case 'Sunday':
                    $startTime = is_null($fulfillmentSchematic->sunday_open) ? '8' : $fulfillmentSchematic->sunday_open;
                    if ($startTime < $earliestStartTime) {
                        $earliestStartTime = $startTime;
                    }
                    break;
                case 'Monday':
                    $startTime = is_null($fulfillmentSchematic->monday_open) ? '8' : $fulfillmentSchematic->monday_open;
                    if ($startTime < $earliestStartTime) {
                        $earliestStartTime = $startTime;
                    }
                    break;
                case 'Tuesday':
                    $startTime = is_null(
                        $fulfillmentSchematic->tuesday_open
                    ) ? '8' : $fulfillmentSchematic->tuesday_open;
                    if ($startTime < $earliestStartTime) {
                        $earliestStartTime = $startTime;
                    }
                    break;
                case 'Wednesday':
                    $startTime = is_null(
                        $fulfillmentSchematic->wednesday_open
                    ) ? '8' : $fulfillmentSchematic->wednesday_open;
                    if ($startTime < $earliestStartTime) {
                        $earliestStartTime = $startTime;
                    }
                    break;
                case 'Thursday':
                    $startTime = is_null(
                        $fulfillmentSchematic->thursday_open
                    ) ? '8' : $fulfillmentSchematic->thursday_open;
                    if ($startTime < $earliestStartTime) {
                        $earliestStartTime = $startTime;
                    }
                    break;
                case 'Friday':
                    $startTime = is_null($fulfillmentSchematic->friday_open) ? '8' : $fulfillmentSchematic->friday_open;
                    if ($startTime < $earliestStartTime) {
                        $earliestStartTime = $startTime;
                    }
                    break;
                case 'Saturday':
                    $startTime = is_null(
                        $fulfillmentSchematic->saturday_open
                    ) ? '8' : $fulfillmentSchematic->saturday_open;
                    if ($startTime < $earliestStartTime) {
                        $earliestStartTime = $startTime;
                    }
                    break;
                default:
                    return '8';
            }
        }
        return $earliestStartTime;
    }

    public function refreshDelivery()
    {
        $now = new DateTime();
        $now->setTimezone(new DateTimeZone('America/New_York'));
        $day = $now->format('l');
        $time = doubleval($now->format('H')) + (doubleval($now->format('i')) / 60);
        $delivery = FulfillmentSchematic::where('delivery', true)->first();

        if (
            !$this->isOffHours([['fulfillment_schematic' => $delivery]], $day, $time)
            && !$this->isClosedDay($now, 'delivery')
        ) {
            $diff = $delivery->{strtolower($day) . '_close'}
                - ($delivery->cutoff_is_hourly ? $delivery->cutoff : $delivery->cutoff * 24);

            $date = (new DateTime)->setTime((int)$diff, ($diff - (int)$diff) * 100);
            return $date->format('Y-m-d') . 'T' . $date->format('G:i');
        } else {
            $day = $this->findNextAvailableDay([['fulfillment_schematic' => $delivery]], $now);

            return $day->format('Y-m-d') . 'T' . $day->format('G:i');
        }
    }

    public function price($shipping_id, $products, $zip, $freeShipping, $discountAmount = 0)
    {
        $zip = $zip ?? settings()->getValue('store_postal_code');
        if (!$zip || !$products->map(function ($product) {
                return $product->shippable;
            })->contains(true)) {
            return [
                'shippingAmount' => 0,
                'original_shipping' => 0
            ];
        }

        $zipCode = ZipCode::getCode($zip);

        if (!$zipCode) {
            return [
                'shippingAmount' => 0,
                'original_shipping' => 0
            ];
        }

        $total = $products->map(function ($product) {
                return $product->price;
            })->sum() - $discountAmount;

        $option = ShippingOptionShippingZone::where([
            'shipping_option_id' => $shipping_id,
            'shipping_zone_id' => $zipCode->shipping_zone_id
        ])->first();

        if (!$option) {
            return [
                'shippingAmount' => 0,
                'original_shipping' => 0
            ];
        }

        $dis = '';
        if ((($option->free_shipping_above && $option->free_shipping_above <= $total) || $freeShipping)) {
            $products = $products->filter(function ($product) {
                return data_get($product->model, 'exclude_free_shipping');
            });

            if (collect(explode(',', settings()->getValue('exclude_from_free_shipping_does_not_apply_to')))->contains(
                $shipping_id
            )) {
                $products = collect();
            }

            if ($products->isEmpty()) {
                $dis = $freeShipping ? 'free' : 'above';
            }
        }

        $billable_weight = $products->map(function ($product) {
            return $product->billable_weight * data_get($product, 'quantity');
        })->sum();

        $price = $option->type == 'flat'
            ? $option->base_rate + $option->price_per_pound
            : $option->base_rate + ($option->priceByPound($billable_weight) ?? 0);
        return [
            'shippingAmount' => $dis == 'free' || $dis == 'above' ? 0.00 : $price,
            'original_shipping' => $dis == 'free' ? $price : 0.00
        ];
    }

    public function productRate($product)
    {
        $options = $this->rates(request()->merge([
            'country' => session()->get('zip_code')['country'] ?? settings()->getValue('store_country'),
            'zip' => session()->get('zip_code')['zip'] ?? settings()->getValue('store_postal_code'),
            'products' => collect([$product->frontEnd]),
            'includeDigital' => true,
        ]));

        return [
            'type' => 'national',
            'store_expires' => $this->refreshPickup(),
            'fulfillment_expires' => $this->refreshDelivery(),
            'pickup' => $options->where('delivery', false)->first(),
            'cheapest' => $options->where('delivery', true)->sortBy('price')->first(),
            'fastest' => $options->where('delivery', true)->sortBy('days')->first(),
            'over_price' => $options
                ->where('free_shipping_above', '!==', null)
                ->where('delivery', true)
                ->pluck('free_shipping_above')
                ->min(),
            'free_over' => $options
                ->where('free_shipping_above', '!==', null)
                ->where('delivery', true)
                ->sortBy('free_shipping_above')
                ->first(),
        ];
    }

    public function rates()
    {
        $request = request();

        $includeDigital = $request->includeDigital ?? false;

        if ($request->country && $request->country != 'US') {
            return InternationalRatesController::rates($request);
        }

        $products = GetFromFrontEnd($request->products);

        if (!$products->map(function ($product) {
                return $product->shippable;
            })->contains(true) && !$includeDigital) {
            return;
        }
        $products_excluded = collect($products)->filter(function ($product) {
            return optional($product->model)->exclude_free_shipping;
        });

        $excluded_billable_weight = $products_excluded->isEmpty() ? null : $products_excluded->map(function ($item) {
            return $item->billable_weight * data_get($item, 'quantity');
        })->sum();
        $billable_weight = $products->map(function ($item) {
            return $item->billable_weight * data_get($item, 'quantity');
        })->sum();
        $total = $products->map(function ($item) {
            return $item->price;
        })->sum();

        $discount = Discount::find($request->discountId);
        $location = ['country' => $request->country, 'postal_code' => $request->zip];
        if ($discount) {
            $discount = $discount->GetSavings($products, $location);
        }

        $personalDuration = (new BagController)->getPersonalTotals($products)['duration'] ?? 0;

        return $this
            ->getOptions($billable_weight, $total, $excluded_billable_weight, $location, $discount, $personalDuration)
            ->sortBy('hours')
            ->values();
    }

    /**
     * POST /shippinginfo
     * Determines available shipping options for an order, along with pricing and estimated arrival date/time
     * @param string zip_code - The 3-digit zip code for the order
     * @param double pounds - The weight of the order in pounds
     * @return array
     * @throws Exception
     */
    public function determineShippingOptions($zip_code, $pounds = 0, $country = 'US')
    {
        $request = request();
        if ($request->country != 'US' && $country != 'US') {
            (new ShipEngineController)
                ->GetEstimatedRate($request->country, $request->zip_code, $request->products);
        }
        // SHIPPING OPTIONS FOR THE REQUESTED SHIPPING ZONE

        $zipCode = ZipCode::getCode($zip_code);

        $shippingZone = ShippingZone::findOrFail(optional($zipCode)->shipping_zone_id);
        $shippingOptionShippingZones = ShippingOptionShippingZone::where([
            'shipping_zone_id' => $shippingZone->id,
            'active' => true,
        ])->get();
        $shippingOptions = [];
        foreach ($shippingOptionShippingZones as $shippingOptionShippingZone) {
            $shippingOption = ShippingOption::find($shippingOptionShippingZone->shipping_option_id);
            $fulfillmentSchematic = $shippingOption->fulfillmentSchematic()->first();
            array_push($shippingOptions, [
                'shipping_option_display' => $shippingOption,
                'fulfillment_schematic' => $fulfillmentSchematic,
                'shipping_schematic' => $shippingOptionShippingZone
            ]);
        }
        // CURRENT DATE-TIME INFORMATION
        $now = new DateTime();
        $now->setTimezone(new DateTimeZone('America/New_York'));
        $day = $now->format('l');
        $time = doubleval($now->format('H')) + (doubleval($now->format('i')) / 60);
        if (
            $this->isOffHours($shippingOptions, $day, $time) || $this->isAfterCutoff($shippingOptions, $day, $time) ||
            $this->isClosedDay($now)
        ) {
            return $this->getShippingInfo(
                $shippingOptions,
                $this->findNextAvailableDay($shippingOptions, $now),
                $pounds
            );
        } else {
            return $this->getShippingInfo($shippingOptions, $now, $pounds);
        }
    }

    /**
     * Determines whether all shipping options are invalid because they are too close to the cutoff time for the provided day and time
     * @param $shippingOptions - The array of shipping options to be processed
     * @param string $day - The day of the week of the order
     * @param double $time - The time of the order
     * @return bool
     */
    public function isAfterCutoff($shippingOptions, $day, $time)
    {
        foreach ($shippingOptions as $shippingOption) {
            $fulfillmentSchematic = $shippingOption['fulfillment_schematic'];
            switch ($day) {
                case 'Sunday':
                    if (!(is_null($fulfillmentSchematic->sunday_close) ||
                        ($time - $fulfillmentSchematic->sunday_close) >
                        ($fulfillmentSchematic->cutoff_is_hourly ? $fulfillmentSchematic->cutoff : ($fulfillmentSchematic->cutoff / 60)))) {
                        return false;
                    }
                    break;
                case 'Monday':
                    if (!(is_null($fulfillmentSchematic->monday_close) ||
                        ($time - $fulfillmentSchematic->monday_close) >
                        ($fulfillmentSchematic->cutoff_is_hourly ? $fulfillmentSchematic->cutoff : ($fulfillmentSchematic->cutoff / 60)))) {
                        return false;
                    }
                    break;
                case 'Tuesday':
                    if (!(is_null($fulfillmentSchematic->tuesday_close) ||
                        ($time - $fulfillmentSchematic->tuesday_close) >
                        ($fulfillmentSchematic->cutoff_is_hourly ? $fulfillmentSchematic->cutoff : ($fulfillmentSchematic->cutoff / 60)))) {
                        return false;
                    }
                    break;
                case 'Wednesday':
                    if (!(is_null($fulfillmentSchematic->wednesday_close) ||
                        ($time - $fulfillmentSchematic->wednesday_close) >
                        ($fulfillmentSchematic->cutoff_is_hourly ? $fulfillmentSchematic->cutoff : ($fulfillmentSchematic->cutoff / 60)))) {
                        return false;
                    }
                    break;
                case 'Thursday':
                    if (!(is_null($fulfillmentSchematic->thursday_close) ||
                        ($time - $fulfillmentSchematic->thursday_close) >
                        ($fulfillmentSchematic->cutoff_is_hourly ? $fulfillmentSchematic->cutoff : ($fulfillmentSchematic->cutoff / 60)))) {
                        return false;
                    }
                    break;
                case 'Friday':
                    if (!(is_null($fulfillmentSchematic->friday_close) ||
                        ($time - $fulfillmentSchematic->friday_close) >
                        ($fulfillmentSchematic->cutoff_is_hourly ? $fulfillmentSchematic->cutoff : ($fulfillmentSchematic->cutoff / 60)))) {
                        return false;
                    }
                    break;
                case 'Saturday':
                    if (!(is_null($fulfillmentSchematic->saturday_close) ||
                        ($time - $fulfillmentSchematic->saturday_close) >
                        ($fulfillmentSchematic->cutoff_is_hourly ? $fulfillmentSchematic->cutoff : ($fulfillmentSchematic->cutoff / 60)))) {
                        return false;
                    }
                    break;
                default:
                    return true;
            }
        }
        return true;
    }

    /**
     * Compiles the shipping info, including name, pricing, and estimated arrival time
     * @param array $shippingOptions - The available shipping options
     * @param $day - The start day of processing for the order
     * @param $pounds - The combined weight of the order in pounds
     * @return array
     */
    public function getShippingInfo($shippingOptions, $day, $pounds)
    {
        $shippingInfo = [];
        $weekday = $day->format('l');
        $time = doubleval($day->format('H')) + (doubleval($day->format('i')) / 60);
        foreach ($shippingOptions as $shippingOption) {
            $shippingOptionDisplay = $shippingOption['shipping_option_display'];
            $shippingSchematic = $shippingOption['shipping_schematic'];
            $price = ($shippingSchematic->priceByPound($pounds)) + $shippingSchematic->base_rate;
            if (
                !$this->isOffHours([$shippingOption], $weekday, $time) &&
                !$this->isAfterCutoff([$shippingOption], $weekday, $time)
            ) {
                array_push($shippingInfo, [
                    'id' => $shippingOptionDisplay->id,
                    'name' => $shippingOptionDisplay->visible_name,
                    'price' => (!is_null($shippingSchematic->free_shipping_above) &&
                        ($price >= $shippingSchematic->free_shipping_above || $shippingSchematic->free_shipping_above === 0))
                        ? 0 :
                        $price,
                    'estimated_arrival' => $this->determineEstimatedArrival($shippingSchematic, $day),
                    'delivery' => !!$shippingOptionDisplay->delivery,
                ]);
            }
        }
        return $shippingInfo;
    }

    /**
     * Determines the date and time the order should arrive given the provided shipping schematic
     * @param ShippingOptionShippingZone $shippingSchematic - The shipping schematic to use for processing
     * @param DateTime $day - The starting date and time for processing the order
     * @return string - formatted YYYY-MM-DDTHH:MM
     * @throws Exception
     */
    public function determineEstimatedArrival($shippingSchematic, $day)
    {
        $interval = $shippingSchematic->duration_is_hourly ?
            ('T' . $shippingSchematic->duration . 'H') : ($shippingSchematic->duration . 'D');
        $arrivalDay = $day->add(new DateInterval('P' . $interval));
        // return $arrivalDay;
        return $arrivalDay->format('Y-m-d') . 'T' . $arrivalDay->format('G:i');
    }

    public function getZone($shippingZoneId)
    {
        return ShippingZone::findOrFail($shippingZoneId);
    }
}
