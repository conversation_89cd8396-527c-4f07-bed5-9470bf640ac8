<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\FulfillmentSchematic;
use App\ShippingOption;
use App\ClosedDay;
use Carbon\Carbon;

class DeliveryTimeReport
{
    protected $shippingController;
    protected $fulfillmentSchematic;
    protected $shippingOption;

    public function __construct()
    {
        $this->shippingController = new \App\Http\Controllers\Api\ShippingController();
        // Get the delivery fulfillment schematic (not pickup)
        $this->fulfillmentSchematic = FulfillmentSchematic::where('delivery', true)->first();
        // Get standard shipping option
        $this->shippingOption = ShippingOption::where('delivery', true)
            ->where('internal_name', 'Standard Shipping')
            ->first();

        if (!$this->fulfillmentSchematic) {
            throw new Exception('No delivery fulfillment schematic found');
        }

        if (!$this->shippingOption) {
            throw new Exception('No standard shipping option found');
        }
    }
    
    public function generateReport()
    {
        $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        $report = [];

        // Set a base date for the current week
        $baseDate = Carbon::now()->startOfWeek();

        foreach ($days as $dayIndex => $day) {
            $currentDate = $baseDate->copy()->addDays($dayIndex);

            for ($hour = 0; $hour < 24; $hour++) {
                $orderTime = $currentDate->copy()->hour($hour)->minute(0)->second(0);

                // Format for display (12-hour format)
                $hourFormatted = $orderTime->format('ga');

                // Create shipping option array for status checks
                $shippingOption = [
                    'fulfillment_schematic' => $this->fulfillmentSchematic,
                    'shipping_option' => $this->shippingOption
                ];

                $day = $orderTime->format('l');
                $time = (float)$orderTime->format('H') + ((float)$orderTime->format('i') / 60);

                // Check order status
                $isOffHours = $this->isOffHours([$shippingOption], $day, $time);
                $isAfterCutoff = $this->isAfterCutoff([$shippingOption], $day, $time);
                $isClosedDay = $this->isClosedDay($orderTime);

                // Calculate delivery date using the shipping controller logic
                $deliveryDate = $this->calculateDeliveryDate($orderTime);

                $report[] = [
                    'order_day' => $orderTime->format('l'),
                    'order_hour' => $hourFormatted,
                    'delivery_date' => $deliveryDate->format('l, F j, Y'),
                    'days_to_delivery' => $orderTime->diffInDays($deliveryDate),
                    'is_off_hours' => $isOffHours,
                    'is_after_cutoff' => $isAfterCutoff,
                    'is_closed_day' => $isClosedDay,
                    'order_timestamp' => $orderTime->format('Y-m-d H:i:s'),
                    'delivery_timestamp' => $deliveryDate->format('Y-m-d H:i:s')
                ];
            }
        }

        return $report;
    }
    
    protected function calculateDeliveryDate($orderTime)
    {
        // Create a mock shipping option array as expected by the controller
        $shippingOption = [
            'fulfillment_schematic' => $this->fulfillmentSchematic,
            'shipping_option' => $this->shippingOption
        ];

        $day = $orderTime->format('l');
        $time = (float)$orderTime->format('H') + ((float)$orderTime->format('i') / 60);

        // Check if order is placed during off hours or after cutoff
        $isOffHours = $this->isOffHours([$shippingOption], $day, $time);
        $isAfterCutoff = $this->isAfterCutoff([$shippingOption], $day, $time);
        $isClosedDay = $this->isClosedDay($orderTime);

        if ($isOffHours || $isAfterCutoff || $isClosedDay) {
            // Find the next available processing day
            $nextAvailableDay = $this->findNextAvailableDay([$shippingOption], $orderTime);
            $processingDate = $nextAvailableDay;
        } else {
            $processingDate = $orderTime;
        }

        // Use the shipping option's duration (from database)
        $shippingDuration = $this->shippingOption->duration ?? 1;

        if ($this->shippingOption->duration_is_hourly) {
            // If duration is in hours, add hours to processing date
            $deliveryDate = $processingDate->copy()->addHours($shippingDuration);
        } else {
            // If duration is in days, add days to processing date
            $deliveryDate = $processingDate->copy()->addDays($shippingDuration);
        }

        // Check if delivery date falls on a non-operational day and adjust
        $attempts = 0;
        while (!$this->isOperationalDay($deliveryDate) && $attempts < 10) {
            $deliveryDate->addDay();
            $attempts++;
        }

        return $deliveryDate;
    }
    
    protected function isOffHours($shippingOptions, $day, $time)
    {
        $fulfillmentSchematic = $shippingOptions[0]['fulfillment_schematic'];
        $dayLower = strtolower($day);
        $openField = "{$dayLower}_open";
        $closeField = "{$dayLower}_close";
        
        if (is_null($fulfillmentSchematic->$openField) || is_null($fulfillmentSchematic->$closeField)) {
            return true;
        }
        
        return $time < $fulfillmentSchematic->$openField || $time > $fulfillmentSchematic->$closeField;
    }
    
    protected function isAfterCutoff($shippingOptions, $day, $time)
    {
        foreach ($shippingOptions as $shippingOption) {
            $fulfillmentSchematic = $shippingOption['fulfillment_schematic'];
            $dayLower = strtolower($day);
            $closeField = "{$dayLower}_close";

            if (is_null($fulfillmentSchematic->$closeField)) {
                continue;
            }

            $cutoffTime = $fulfillmentSchematic->$closeField -
                ($fulfillmentSchematic->cutoff_is_hourly ? $fulfillmentSchematic->cutoff : ($fulfillmentSchematic->cutoff / 60));

            if ($time <= $cutoffTime) {
                return false;
            }
        }
        return true;
    }
    
    protected function isClosedDay($date)
    {
        return ClosedDay::whereDate('date', $date->format('Y-m-d'))->exists();
    }
    
    protected function findNextAvailableDay($shippingOptions, $now)
    {
        $nextDay = $now->copy()->addDay();
        $attempts = 0;

        while ($attempts < 30) {
            if (!$this->isClosedDay($nextDay) && $this->hasStartTimes($shippingOptions, $nextDay->format('l'))) {
                // Set to opening time
                $startTime = $this->getEarliestStartTime($shippingOptions, $nextDay->format('l'));
                if (!is_null($startTime)) {
                    $hours = floor($startTime);
                    $minutes = floor(($startTime - $hours) * 60);
                    $nextDay->hour($hours)->minute($minutes)->second(0);
                    return $nextDay;
                }
            }

            $nextDay->addDay();
            $attempts++;
        }

        return $nextDay;
    }

    protected function hasStartTimes($shippingOptions, $day)
    {
        foreach ($shippingOptions as $shippingOption) {
            $fulfillmentSchematic = $shippingOption['fulfillment_schematic'];
            $dayLower = strtolower($day);
            $openField = "{$dayLower}_open";
            $closeField = "{$dayLower}_close";

            if (!(is_null($fulfillmentSchematic->$openField) && is_null($fulfillmentSchematic->$closeField))) {
                return true;
            }
        }
        return false;
    }

    protected function getEarliestStartTime($shippingOptions, $day)
    {
        $earliestTime = null;

        foreach ($shippingOptions as $shippingOption) {
            $fulfillmentSchematic = $shippingOption['fulfillment_schematic'];
            $dayLower = strtolower($day);
            $openField = "{$dayLower}_open";

            if (!is_null($fulfillmentSchematic->$openField)) {
                if (is_null($earliestTime) || $fulfillmentSchematic->$openField < $earliestTime) {
                    $earliestTime = $fulfillmentSchematic->$openField;
                }
            }
        }

        return $earliestTime;
    }
    
    protected function isOperationalDay($date)
    {
        $dayOfWeek = $date->dayOfWeek;
        $operationalDays = explode(',', $this->shippingOption->operational_days);
        
        return in_array($dayOfWeek, $operationalDays);
    }
    
    public function printReport()
    {
        $report = $this->generateReport();

        echo "COMPREHENSIVE DELIVERY TIME REPORT\n";
        echo "==================================\n";
        echo "Generated: " . date('Y-m-d H:i:s') . "\n";
        echo "Fulfillment Schematic: " . ($this->fulfillmentSchematic->name ?? 'N/A') . "\n";
        echo "Shipping Option: " . ($this->shippingOption->visible_name ?? 'N/A') . "\n";
        echo "Cutoff Time: " . ($this->fulfillmentSchematic->cutoff ?? 'N/A') . " " .
             ($this->fulfillmentSchematic->cutoff_is_hourly ? 'hours' : 'minutes') . " before close\n";
        echo "Shipping Duration: " . ($this->shippingOption->duration ?? 'N/A') . " " .
             ($this->shippingOption->duration_is_hourly ? 'hours' : 'days') . "\n";
        echo "Operational Days: " . ($this->shippingOption->operational_days ?? 'N/A') . "\n\n";

        $currentDay = '';
        foreach ($report as $entry) {
            if ($currentDay !== $entry['order_day']) {
                if ($currentDay !== '') echo "\n";
                echo "=== {$entry['order_day']} ===\n";
                $currentDay = $entry['order_day'];
            }

            $status = '';
            if ($entry['is_off_hours']) $status .= '[OFF-HOURS] ';
            if ($entry['is_after_cutoff']) $status .= '[AFTER-CUTOFF] ';
            if ($entry['is_closed_day']) $status .= '[CLOSED-DAY] ';

            echo "{$entry['order_hour']} - Delivery on {$entry['delivery_date']} ({$entry['days_to_delivery']} days) {$status}\n";
        }

        echo "\n=== SUMMARY ===\n";
        $this->printSummary($report);
    }

    protected function printSummary($report)
    {
        $deliveryDays = [];
        $statusCounts = [
            'normal' => 0,
            'off_hours' => 0,
            'after_cutoff' => 0,
            'closed_day' => 0
        ];

        foreach ($report as $entry) {
            $deliveryDays[] = $entry['days_to_delivery'];

            if ($entry['is_off_hours']) $statusCounts['off_hours']++;
            elseif ($entry['is_after_cutoff']) $statusCounts['after_cutoff']++;
            elseif ($entry['is_closed_day']) $statusCounts['closed_day']++;
            else $statusCounts['normal']++;
        }

        echo "Average delivery time: " . round(array_sum($deliveryDays) / count($deliveryDays), 2) . " days\n";
        echo "Min delivery time: " . min($deliveryDays) . " days\n";
        echo "Max delivery time: " . max($deliveryDays) . " days\n\n";

        echo "Order Status Distribution:\n";
        echo "- Normal processing: {$statusCounts['normal']} orders\n";
        echo "- Off-hours orders: {$statusCounts['off_hours']} orders\n";
        echo "- After-cutoff orders: {$statusCounts['after_cutoff']} orders\n";
        echo "- Closed-day orders: {$statusCounts['closed_day']} orders\n";
    }
}

// Generate and print the report
$reporter = new DeliveryTimeReport();
$reporter->printReport();