<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\FulfillmentSchematic;
use App\ShippingOption;
use App\ClosedDay;
use Carbon\Carbon;
use DateInterval;
use DateTime;
use DateTimeZone;

class DeliveryTimeReport
{
    protected $shippingController;
    protected $fulfillmentSchematic;
    protected $shippingOptions;
    
    public function __construct()
    {
        $this->shippingController = new \App\Http\Controllers\Api\ShippingController();
        // Get the delivery fulfillment schematic (not pickup)
        $this->fulfillmentSchematic = FulfillmentSchematic::where('delivery', true)->first();
        // Get standard shipping option
        $this->shippingOption = ShippingOption::where('delivery', true)
            ->where('internal_name', 'Standard Shipping')
            ->first();
    }
    
    public function generateReport()
    {
        $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        $report = [];
        
        // Set a base date for the current week
        $baseDate = Carbon::now()->startOfWeek();
        
        foreach ($days as $dayIndex => $day) {
            $currentDate = $baseDate->copy()->addDays($dayIndex);
            
            for ($hour = 0; $hour < 24; $hour++) {
                $orderTime = $currentDate->copy()->hour($hour)->minute(0)->second(0);
                
                // Format for display (12-hour format)
                $hourFormatted = $orderTime->format('ga');
                
                // Calculate delivery date using the shipping controller logic
                $deliveryDate = $this->calculateDeliveryDate($orderTime);
                
                $report[] = [
                    'order_day' => $day,
                    'order_hour' => $hourFormatted,
                    'delivery_date' => $deliveryDate->format('l, F j, Y'),
                    'days_to_delivery' => $orderTime->diffInDays($deliveryDate)
                ];
            }
        }
        
        return $report;
    }
    
    protected function calculateDeliveryDate($orderTime)
    {
        // Create a mock shipping option array as expected by the controller
        $shippingOption = [
            'fulfillment_schematic' => $this->fulfillmentSchematic,
            'shipping_option' => $this->shippingOption
        ];
        
        $day = $orderTime->format('l');
        $time = (float)$orderTime->format('H') + ((float)$orderTime->format('i') / 60);
        
        // Check if order is placed during off hours or after cutoff
        $isOffHours = $this->isOffHours([$shippingOption], $day, $time);
        $isAfterCutoff = $this->isAfterCutoff([$shippingOption], $day, $time);
        $isClosedDay = $this->isClosedDay($orderTime);
        
        if ($isOffHours || $isAfterCutoff || $isClosedDay) {
            // Find the next available processing day
            $nextAvailableDay = $this->findNextAvailableDay([$shippingOption], $orderTime);
            $processingDate = $nextAvailableDay;
        } else {
            $processingDate = $orderTime;
        }
        
        // Add shipping duration (typically 1-2 days for standard shipping)
        $shippingDuration = 1; // Adjust based on your standard shipping duration
        $deliveryDate = $processingDate->copy()->addDays($shippingDuration);
        
        // Check if delivery date falls on a non-operational day
        while (!$this->isOperationalDay($deliveryDate)) {
            $deliveryDate->addDay();
        }
        
        return $deliveryDate;
    }
    
    protected function isOffHours($shippingOptions, $day, $time)
    {
        $fulfillmentSchematic = $shippingOptions[0]['fulfillment_schematic'];
        $dayLower = strtolower($day);
        $openField = "{$dayLower}_open";
        $closeField = "{$dayLower}_close";
        
        if (is_null($fulfillmentSchematic->$openField) || is_null($fulfillmentSchematic->$closeField)) {
            return true;
        }
        
        return $time < $fulfillmentSchematic->$openField || $time > $fulfillmentSchematic->$closeField;
    }
    
    protected function isAfterCutoff($shippingOptions, $day, $time)
    {
        $fulfillmentSchematic = $shippingOptions[0]['fulfillment_schematic'];
        $dayLower = strtolower($day);
        $closeField = "{$dayLower}_close";
        
        if (is_null($fulfillmentSchematic->$closeField)) {
            return true;
        }
        
        $cutoffTime = $fulfillmentSchematic->$closeField - 
            ($fulfillmentSchematic->cutoff_is_hourly ? $fulfillmentSchematic->cutoff : ($fulfillmentSchematic->cutoff / 60));
        
        return $time > $cutoffTime;
    }
    
    protected function isClosedDay($date)
    {
        return ClosedDay::whereDate('date', $date->format('Y-m-d'))->exists();
    }
    
    protected function findNextAvailableDay($shippingOptions, $now)
    {
        $nextDay = $now->copy()->addDay();
        $attempts = 0;
        
        while ($attempts < 10) {
            if (!$this->isClosedDay($nextDay) && $this->isOperationalDay($nextDay)) {
                // Set to opening time
                $dayLower = strtolower($nextDay->format('l'));
                $openField = "{$dayLower}_open";
                $openTime = $shippingOptions[0]['fulfillment_schematic']->$openField;
                
                if (!is_null($openTime)) {
                    $hours = floor($openTime);
                    $minutes = floor(($openTime - $hours) * 60);
                    $nextDay->hour($hours)->minute($minutes)->second(0);
                    return $nextDay;
                }
            }
            
            $nextDay->addDay();
            $attempts++;
        }
        
        return $nextDay;
    }
    
    protected function isOperationalDay($date)
    {
        $dayOfWeek = $date->dayOfWeek;
        $operationalDays = explode(',', $this->shippingOption->operational_days);
        
        return in_array($dayOfWeek, $operationalDays);
    }
    
    public function printReport()
    {
        $report = $this->generateReport();
        
        echo "DELIVERY TIME REPORT\n";
        echo "===================\n\n";
        
        foreach ($report as $entry) {
            echo "{$entry['order_day']} {$entry['order_hour']} - Delivery on {$entry['delivery_date']} ({$entry['days_to_delivery']} days)\n";
        }
    }
}

// Generate and print the report
$reporter = new DeliveryTimeReport();
$reporter->printReport();